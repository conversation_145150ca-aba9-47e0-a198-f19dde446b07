// Test script to verify desktop tabulator behavior is restored
// Run this in browser console on the search page

console.log('🧪 Testing Desktop Tabulator Behavior');

// 1. Clear the cache to start fresh
localStorage.removeItem('colony-tabulator-cache-search');
console.log('✅ Cleared tabulator cache');

// 2. Check if we're on desktop (not Android)
const isAndroid = /Android/i.test(navigator.userAgent);
console.log('📱 Is Android:', isAndroid);
console.log('🖥️ Testing desktop behavior:', !isAndroid);

// 3. Wait for page to reload and tabulator to be ready
setTimeout(() => {
  console.log('🔍 Checking tabulator instance...');
  
  // Find the tabulator table
  const tabulatorElement = document.querySelector('.tabulator');
  if (!tabulatorElement) {
    console.error('❌ No tabulator found on page');
    return;
  }
  
  console.log('✅ Found tabulator element');
  
  // Check for rows
  const rows = tabulatorElement.querySelectorAll('.tabulator-row');
  console.log(`📊 Found ${rows.length} table rows`);
  
  if (rows.length === 0) {
    console.log('⚠️ No data rows found. Try performing a search first.');
    return;
  }
  
  // Test 1: Check if download icon exists and is clickable
  const firstRow = rows[0];
  const downloadIcon = firstRow.querySelector('.tabulator-cell:first-child svg, .tabulator-cell:first-child img');
  
  if (downloadIcon) {
    console.log('✅ Found download icon in first column');
    
    // Test clicking the download icon
    console.log('🖱️ Testing download icon click...');
    downloadIcon.click();
    console.log('✅ Download icon click triggered (check for download/toast)');
  } else {
    console.log('⚠️ No download icon found in first column');
  }
  
  // Test 2: Check if name column is clickable for metadata dialog
  const nameCell = firstRow.querySelector('.tabulator-cell:nth-child(2)');
  if (nameCell) {
    console.log('✅ Found name cell');
    
    setTimeout(() => {
      console.log('🖱️ Testing name cell click for metadata dialog...');
      nameCell.click();
      
      // Check if modal opened
      setTimeout(() => {
        const modal = document.querySelector('#fileMetadataModal[open], .modal[open]');
        if (modal) {
          console.log('✅ Metadata dialog opened successfully');
          // Close the modal
          const closeBtn = modal.querySelector('button[type="button"], .btn');
          if (closeBtn) closeBtn.click();
        } else {
          console.log('❌ Metadata dialog did not open');
        }
      }, 100);
    }, 1000);
  } else {
    console.log('⚠️ No name cell found');
  }
  
}, 2000);

console.log('🕐 Test will run in 2 seconds...');
