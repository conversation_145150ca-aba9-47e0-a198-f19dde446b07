Found app bundle: src-tauri/target/x86_64-apple-darwin/release/bundle/macos/Colony.app
Created zip archive: src-tauri/target/x86_64-apple-darwin/release/bundle/macos/Colony.zip
Verifying code signature...
src-tauri/target/x86_64-apple-darwin/release/bundle/macos/Colony.app: valid on disk
src-tauri/target/x86_64-apple-darwin/release/bundle/macos/Colony.app: satisfies its Designated Requirement
Checking hardened runtime...
Executable=/Users/<USER>/work/colony/colony/src-tauri/target/x86_64-apple-darwin/release/bundle/macos/Colony.app/Contents/MacOS/colony-app
[Dict]
	[Key] com.apple.security.app-sandbox
	[Value]
		[Bool] true
	[Key] com.apple.security.cs.allow-jit
	[Value]
		[Bool] true
	[Key] com.apple.security.cs.allow-unsigned-executable-memory
	[Value]
		[Bool] true
	[Key] com.apple.security.cs.disable-library-validation
	[Value]
		[Bool] true
	[Key] com.apple.security.device.camera
	[Value]
		[Bool] false
	[Key] com.apple.security.device.microphone
	[Value]
		[Bool] false
	[Key] com.apple.security.files.downloads.read-write
	[Value]
		[Bool] true
	[Key] com.apple.security.files.user-selected.executable
	[Value]
		[Bool] true
	[Key] com.apple.security.files.user-selected.read-write
	[Value]
		[Bool] true
	[Key] com.apple.security.network.client
	[Value]
		[Bool] true
	[Key] com.apple.security.personal-information.addressbook
	[Value]
		[Bool] false
	[Key] com.apple.security.personal-information.calendars
	[Value]
		[Bool] false
	[Key] com.apple.security.personal-information.location
	[Value]
		[Bool] false
Submitting for notarization...
Notarization status: Accepted
Stapling notarization ticket...
Processing: /Users/<USER>/work/colony/colony/src-tauri/target/x86_64-apple-darwin/release/bundle/macos/Colony.app
Processing: /Users/<USER>/work/colony/colony/src-tauri/target/x86_64-apple-darwin/release/bundle/macos/Colony.app
The staple and validate action worked!
Verifying notarization...
Processing: /Users/<USER>/work/colony/colony/src-tauri/target/x86_64-apple-darwin/release/bundle/macos/Colony.app
The validate action worked!
src-tauri/target/x86_64-apple-darwin/release/bundle/macos/Colony.app: accepted
source=Notarized Developer ID
Found DMG: src-tauri/target/x86_64-apple-darwin/release/bundle/dmg/Colony_1.1.1_x64.dmg
[03:27:04.609Z] Debug [MAIN] Running notarytool version: unknown (0), date: 2025-08-02T03:27:04Z, command: /Applications/Xcode_15.4.app/Contents/Developer/usr/bin/notarytool submit src-tauri/target/x86_64-apple-darwin/release/bundle/dmg/Colony_1.1.1_x64.dmg --key /Users/<USER>/.appstoreconnect/private_keys/AuthKey_***.p8 --key-id *** --issuer *** --wait --timeout 30m --verbose
Conducting pre-submission checks for Colony_1.1.1_x64.dmg and initiating connection to the Apple notary service...
[03:27:04.617Z] Debug [PREFLIGHT] Colony_1.1.1_x64.dmg is potentially a disk image, doing additional checks for UDIF.
[03:27:04.618Z] Debug [PREFLIGHT] The disk image has the proper UDIF magic and passes the UDIF tests.
[03:27:04.619Z] Debug [PREFLIGHT] Finished completing determination of file type for Colony_1.1.1_x64.dmg. Operation took 7ms.
[03:27:04.620Z] Info [API] Initialized Notary API with base URL: https://appstoreconnect.apple.com/notary/v2/
[03:27:04.621Z] Debug [CTXMGR] Created temporary directory at: file:///var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/TemporaryItems/NSIRD_notarytool_wNU0wv/
[03:27:04.622Z] Debug [CTXMGR] Copied src-tauri/target/x86_64-apple-darwin/release/bundle/dmg/Colony_1.1.1_x64.dmg -- file:///Users/<USER>/work/colony/colony/ to file:///var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/TemporaryItems/NSIRD_notarytool_wNU0wv/Colony_1.1.1_x64.dmg
[03:27:04.622Z] Debug [FILEHASH] Generating md5 and sha256 digest with buffer size 4161536 for file:///var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/TemporaryItems/NSIRD_notarytool_wNU0wv/Colony_1.1.1_x64.dmg
[03:27:04.703Z] Debug [FILEHASH] MD5 digest: 2c9f169f3b41077445c4b32e2683b759
[03:27:04.703Z] Debug [FILEHASH] Base64-encoded MD5 Digest: LJ8WnztBB3RFxLMuJoO3WQ==
[03:27:04.703Z] Debug [FILEHASH] SHA256 digest: e6c3d9a01cb7d1e298f6d199040c4fd8533266987e1b8140a4abd19793ecbe05
[03:27:04.704Z] Debug [FILEHASH] Finished generating file hashes for Colony_1.1.1_x64.dmg. Operation took 81ms.
[03:27:04.704Z] Debug [API] Sending submissions payload: submissionPayload(submissionName: "Colony_1.1.1_x64.dmg", md5: "2c9f169f3b41077445c4b32e2683b759", sha256: "e6c3d9a01cb7d1e298f6d199040c4fd8533266987e1b8140a4abd19793ecbe05", notifications: [])
[03:27:04.705Z] Info [API] Preparing POST request to URL: https://appstoreconnect.apple.com/notary/v2/submissions?, Custom Headers: private<Dictionary<String, String>>
[03:27:04.705Z] Debug [JWT] Generating new JWT for key ID: ***.
[03:27:04.705Z] Info [JWT] Caching newly generated JWT. key ID: ***, JWT: private<String>
[03:27:04.706Z] Debug [AUTHENTICATION] Authenticating request with App Store Connect API credentials. Key ID: ***, Issuer ID: ***
[03:27:04.706Z] Debug [TASKMANAGER] Starting Task Manager loop to wait for asynchronous HTTP calls.
[03:27:05.192Z] Debug [API] Received response status code: 200, message: no error, URL: https://appstoreconnect.apple.com/notary/v2/submissions?, Correlation Key: NI2IRDRNJANPKTGU4HICBCNWE4
[03:27:05.193Z] Debug [TASKMANAGER] Completed Task with ID 1 has received a parsable response.
[03:27:05.193Z] Debug [TASKMANAGER] Ending Task Manager loop.
[03:27:05.193Z] Debug [MAIN] Received Submission Response: private<NewSubmission>
Submission ID received
  id: 83204aea-77d5-43cb-a2d4-4b0441897367
[03:27:05.195Z] Debug [UPLOAD] File sized 25.8 MB w/ min calculated part size: 26.4 KB. Multipart upload chunk size: 5.00 MB
[03:27:05.196Z] Debug [UPLOAD] Received new upload status: Initiating
[03:27:05.196Z] Debug [UPLOAD] Submission with ID 83204AEA-77D5-43CB-A2D4-4B0441897367 is being uploaded to bucket notary-submissions-prod at key prod/AROARQRX7CZS3PRF6ZA5L:83204aea-77d5-43cb-a2d4-4b0441897367
[03:27:05.196Z] Debug [UPLOAD] File sized 25.8 MB w/ min calculated part size: 26.4 KB. Multipart upload chunk size: 5.00 MB
[03:27:05.197Z] Info [UPLOAD] Starting S3 multipart upload of file at 'file:///var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/TemporaryItems/NSIRD_notarytool_wNU0wv/Colony_1.1.1_x64.dmg' with part size 5 MB to Bucket: notary-submissions-prod, Key: prod/AROARQRX7CZS3PRF6ZA5L:83204aea-77d5-43cb-a2d4-4b0441897367
[03:27:05.197Z] Debug [UPLOAD] Received new upload status: Uploading
[03:27:05.620Z] Info [UPLOAD] Because stdout was redirected, detailed upload progress will not be displayed.
[03:27:06.455Z] Debug [UPLOAD] Completed [1/6] chunks of a multipart upload.
[03:27:07.115Z] Debug [UPLOAD] Completed [2/6] chunks of a multipart upload.
[03:27:08.074Z] Debug [UPLOAD] Completed [3/6] chunks of a multipart upload.
[03:27:08.384Z] Debug [UPLOAD] Completed [4/6] chunks of a multipart upload.
[03:27:08.652Z] Debug [UPLOAD] Completed [5/6] chunks of a multipart upload.
[03:27:09.011Z] Debug [UPLOAD] Completed [6/6] chunks of a multipart upload.
[03:27:09.012Z] Debug [UPLOAD] Received new upload status: Succeeded
[03:27:09.014Z] Debug [UPLOAD] multipart upload etag: "a7cbeb911317ca6fb4e269038de925ef-6"
[03:27:09.015Z] Info [UPLOAD] Multipart upload process has completed successfully.
[03:27:09.016Z] Info [UPLOAD] Attempting to shutdown local S3 upload service.
[03:27:09.017Z] Info [UPLOAD] Successfully shutdown local S3 upload service.
Successfully uploaded file
  id: 83204aea-77d5-43cb-a2d4-4b0441897367
  path: /Users/<USER>/work/colony/colony/src-tauri/target/x86_64-apple-darwin/release/bundle/dmg/Colony_1.1.1_x64.dmg
[03:27:09.018Z] Info [MAIN] Wait operation will timeout after 1800.0 second(s) have passed.
Waiting for processing to complete. Wait timeout is set to 1800.0 second(s).
[03:27:09.019Z] Info [API] Beginning to wait for submission id: 83204aea-77d5-43cb-a2d4-4b0441897367
[03:27:09.021Z] Debug [API] Timeout is set. Polling will exit after 1800 second(s) have passed.
[03:27:09.022Z] Info [API] Preparing GET request to URL: https://appstoreconnect.apple.com/notary/v2/submissions/83204aea-77d5-43cb-a2d4-4b0441897367?, Parameters: [:], Custom Headers: private<Dictionary<String, String>>
[03:27:09.023Z] Debug [JWT] Using cached JWT value for key ID: ***
[03:27:09.023Z] Debug [AUTHENTICATION] Authenticating request with App Store Connect API credentials. Key ID: ***, Issuer ID: ***
[03:27:09.025Z] Debug [TASKMANAGER] Starting Task Manager loop to wait for asynchronous HTTP calls.
[03:27:09.199Z] Debug [API] Received response status code: 200, message: no error, URL: https://appstoreconnect.apple.com/notary/v2/submissions/83204aea-77d5-43cb-a2d4-4b0441897367?, Correlation Key: EO6CJZMNUWN342YMRPM3T52VDU
[03:27:09.199Z] Debug [TASKMANAGER] Completed Task with ID 2 has received a parsable response.
[03:27:09.199Z] Debug [TASKMANAGER] Ending Task Manager loop.
[03:27:09.200Z] Info [API] Initial status: In Progress)
[03:27:09.200Z] Info [API] Waiting 5 seconds before next poll...
[03:27:14.306Z] Info [API] Preparing GET request to URL: https://appstoreconnect.apple.com/notary/v2/submissions/83204aea-77d5-43cb-a2d4-4b0441897367?, Parameters: [:], Custom Headers: private<Dictionary<String, String>>
[03:27:14.308Z] Debug [JWT] Using cached JWT value for key ID: ***
[03:27:14.309Z] Debug [AUTHENTICATION] Authenticating request with App Store Connect API credentials. Key ID: ***, Issuer ID: ***
[03:27:14.311Z] Debug [TASKMANAGER] Starting Task Manager loop to wait for asynchronous HTTP calls.
[03:27:14.520Z] Debug [API] Received response status code: 200, message: no error, URL: https://appstoreconnect.apple.com/notary/v2/submissions/83204aea-77d5-43cb-a2d4-4b0441897367?, Correlation Key: PUR2TPA2Z36EPUBXCW6SPPQENM
[03:27:14.521Z] Debug [TASKMANAGER] Completed Task with ID 3 has received a parsable response.

[03:27:14.521Z] Debug [TASKMANAGER] Ending Task Manager loop.
[03:27:14.521Z] Info [API] Received new status: In Progress
[03:27:14.522Z] Info [API] Waiting 5 seconds before next poll...
[03:27:19.583Z] Info [API] Preparing GET request to URL: https://appstoreconnect.apple.com/notary/v2/submissions/83204aea-77d5-43cb-a2d4-4b0441897367?, Parameters: [:], Custom Headers: private<Dictionary<String, String>>
[03:27:19.585Z] Debug [JWT] Using cached JWT value for key ID: ***
[03:27:19.586Z] Debug [AUTHENTICATION] Authenticating request with App Store Connect API credentials. Key ID: ***, Issuer ID: ***
[03:27:19.588Z] Debug [TASKMANAGER] Starting Task Manager loop to wait for asynchronous HTTP calls.
[03:27:19.798Z] Debug [API] Received response status code: 200, message: no error, URL: https://appstoreconnect.apple.com/notary/v2/submissions/83204aea-77d5-43cb-a2d4-4b0441897367?, Correlation Key: IL5GIR2VUWHVIZ5O7R7AUYJEGM
[03:27:19.798Z] Debug [TASKMANAGER] Completed Task with ID 4 has received a parsable response.
Current status: In Progress...
[03:27:19.798Z] Debug [TASKMANAGER] Ending Task Manager loop.
[03:27:19.799Z] Info [API] Received new status: In Progress
[03:27:19.799Z] Info [API] Waiting 5 seconds before next poll...
[03:27:24.885Z] Info [API] Preparing GET request to URL: https://appstoreconnect.apple.com/notary/v2/submissions/83204aea-77d5-43cb-a2d4-4b0441897367?, Parameters: [:], Custom Headers: private<Dictionary<String, String>>
[03:27:24.886Z] Debug [JWT] Using cached JWT value for key ID: ***
[03:27:24.887Z] Debug [AUTHENTICATION] Authenticating request with App Store Connect API credentials. Key ID: ***, Issuer ID: ***
[03:27:24.888Z] Debug [TASKMANAGER] Starting Task Manager loop to wait for asynchronous HTTP calls.
[03:27:25.357Z] Debug [API] Received response status code: 200, message: no error, URL: https://appstoreconnect.apple.com/notary/v2/submissions/83204aea-77d5-43cb-a2d4-4b0441897367?, Correlation Key: MHJ6QH3Q2TRQVXXZTVM6RSQV24
[03:27:25.357Z] Debug [TASKMANAGER] Completed Task with ID 5 has received a parsable response.
Current status: In Progress....
[03:27:25.357Z] Debug [TASKMANAGER] Ending Task Manager loop.
[03:27:25.358Z] Info [API] Received new status: In Progress
[03:27:25.358Z] Info [API] Waiting 5 seconds before next poll...
[03:27:30.509Z] Info [API] Preparing GET request to URL: https://appstoreconnect.apple.com/notary/v2/submissions/83204aea-77d5-43cb-a2d4-4b0441897367?, Parameters: [:], Custom Headers: private<Dictionary<String, String>>
[03:27:30.511Z] Debug [JWT] Using cached JWT value for key ID: ***
[03:27:30.512Z] Debug [AUTHENTICATION] Authenticating request with App Store Connect API credentials. Key ID: ***, Issuer ID: ***
[03:27:30.514Z] Debug [TASKMANAGER] Starting Task Manager loop to wait for asynchronous HTTP calls.
[03:27:30.693Z] Debug [API] Received response status code: 200, message: no error, URL: https://appstoreconnect.apple.com/notary/v2/submissions/83204aea-77d5-43cb-a2d4-4b0441897367?, Correlation Key: RT7JYQEP2DWNAYU42TUJ25NBYY
[03:27:30.693Z] Debug [TASKMANAGER] Completed Task with ID 6 has received a parsable response.
Current status: In Progress.....
[03:27:30.694Z] Debug [TASKMANAGER] Ending Task Manager loop.
[03:27:30.694Z] Info [API] Received new status: In Progress
[03:27:30.694Z] Info [API] Waiting 5 seconds before next poll...
[03:27:35.712Z] Info [API] Preparing GET request to URL: https://appstoreconnect.apple.com/notary/v2/submissions/83204aea-77d5-43cb-a2d4-4b0441897367?, Parameters: [:], Custom Headers: private<Dictionary<String, String>>
[03:27:35.714Z] Debug [JWT] Using cached JWT value for key ID: ***
[03:27:35.715Z] Debug [AUTHENTICATION] Authenticating request with App Store Connect API credentials. Key ID: ***, Issuer ID: ***
[03:27:35.717Z] Debug [TASKMANAGER] Starting Task Manager loop to wait for asynchronous HTTP calls.
[03:27:35.934Z] Debug [API] Received response status code: 200, message: no error, URL: https://appstoreconnect.apple.com/notary/v2/submissions/83204aea-77d5-43cb-a2d4-4b0441897367?, Correlation Key: BCPCTDURXIIPXF5MRHQAURLTQY
[03:27:35.934Z] Debug [TASKMANAGER] Completed Task with ID 7 has received a parsable response.
Current status: In Progress......
[03:27:35.934Z] Debug [TASKMANAGER] Ending Task Manager loop.
[03:27:35.935Z] Info [API] Received new status: In Progress
[03:27:35.935Z] Info [API] Waiting 8 seconds before next poll...
[03:27:43.940Z] Info [API] Preparing GET request to URL: https://appstoreconnect.apple.com/notary/v2/submissions/83204aea-77d5-43cb-a2d4-4b0441897367?, Parameters: [:], Custom Headers: private<Dictionary<String, String>>
[03:27:43.942Z] Debug [JWT] Using cached JWT value for key ID: ***
[03:27:43.943Z] Debug [AUTHENTICATION] Authenticating request with App Store Connect API credentials. Key ID: ***, Issuer ID: ***
[03:27:43.945Z] Debug [TASKMANAGER] Starting Task Manager loop to wait for asynchronous HTTP calls.
[03:27:44.124Z] Debug [API] Received response status code: 200, message: no error, URL: https://appstoreconnect.apple.com/notary/v2/submissions/83204aea-77d5-43cb-a2d4-4b0441897367?, Correlation Key: RIKBI3RKIQVY55L6NXU2M4JLFQ
[03:27:44.124Z] Debug [TASKMANAGER] Completed Task with ID 8 has received a parsable response.
Current status: In Progress.......
[03:27:44.124Z] Debug [TASKMANAGER] Ending Task Manager loop.
Current status: Accepted........Processing complete
  id: 83204aea-77d5-43cb-a2d4-4b0441897367
  status: Accepted
[03:27:44.125Z] Info [API] Received new status: Accepted
[03:27:44.125Z] Info [API] Submission in terminal status: Accepted
[03:27:44.126Z] Debug [CTXMGR] Removed temporary directory: Optional(file:///var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/TemporaryItems/NSIRD_notarytool_wNU0wv/)

Processing: /Users/<USER>/work/colony/colony/src-tauri/target/x86_64-apple-darwin/release/bundle/dmg/Colony_1.1.1_x64.dmg
Processing: /Users/<USER>/work/colony/colony/src-tauri/target/x86_64-apple-darwin/release/bundle/dmg/Colony_1.1.1_x64.dmg
The staple and validate action worked!
Uploading to App Store Connect...
2025-08-02 03:27:45.090 Verbose logging enabled.
2025-08-02 03:27:45.093 Will look for transporter at executable bundle relative path: /Applications/Xcode_15.4.app/Contents/SharedFrameworks/ContentDeliveryServices.framework/itms/bin/iTMSTransporter
2025-08-02 03:27:45.093 Looking for Transporter at path: /Applications/Xcode_15.4.app/Contents/SharedFrameworks/ContentDeliveryServices.framework/itms/bin/iTMSTransporter
2025-08-02 03:27:45.093 Looking for Transporter at path: /Applications/Xcode_15.4.app/Contents/Developer/usr/bin/../SharedFrameworks/ContentDeliveryServices.framework/itms/bin/iTMSTransporter
2025-08-02 03:27:45.093 Looking for Transporter at path: /Applications/Xcode_15.4.app/Contents/Developer/usr/bin/../itms/bin/iTMSTransporter
2025-08-02 03:27:45.093 Looking for Transporter at path: /Applications/Transporter.app/Contents/itms/bin/iTMSTransporter
2025-08-02 03:27:45.093 Looking for Transporter at path: /usr/local/itms/bin/iTMSTransporter
2025-08-02 03:27:45.093 Using default transporter location: /usr/local/itms/bin/iTMSTransporter
2025-08-02 03:27:45.093 iTMSTransporter is not available. Using REST APIs.
2025-08-02 03:27:45.098 *** SESSION AUTH: Webservice call to create a transporter session.
2025-08-02 03:27:45.127 
=======================
WEB SERVICE REQUEST 'authenticateForSession'
           URL: https://contentdelivery.itunes.apple.com/WebObjects/MZLabelService.woa/json/MZContentDeliveryService
       timeout: 1800
        method: POST
   httpHeaders: {
    Authorization = "***";
    "Content-Length" = 445;
    "Content-Type" = "application/json";
    "x-request-id" = "20250802032745-020";
    "x-tx-client-name" = altool;
    "x-tx-client-version" = "7.006 (15006)";
    "x-tx-method" = authenticateForSession;
}
      httpBody: {
    id = "20250802032745-020";
    jsonrpc = "2.0";
    method = authenticateForSession;
    params =     {
        Application = altool;
        ApplicationBundleId = "com.apple.itunes.altool";
        FrameworkVersions =         {
            "com.apple.itunes.connect.ITunesConnectFoundation" = "7.006 (15006)";
            "com.apple.itunes.connect.ITunesConnectFoundationExtras" = "7.006 (15006)";
            "com.apple.itunes.connect.ITunesPackage" = "7.006 (15006)";
        };
        OSIdentifier = "Mac OS X 14.7.6 (arm64)";
        Version = "7.006 (15006)";
    };
}
=======================
2025-08-02 03:27:45.840 
=======================
WEB SERVICE RESPONSE (authenticateForSession):
 status code: 200 (no error)
   MIME/TYPE: application/json
 httpHeaders: {
    "Accept-Language" = "en-US,en;q=0.9";
    Authorization = "***";
    "Cache-Control" = "private, no-cache, no-store, must-revalidate, max-age=0";
    "Content-Encoding" = gzip;
    "Content-Length" = 1331;
    "Content-Type" = "application/json";
    Date = "Sat, 02 Aug 2025 03:27:45 GMT";
    Expires = "Thu, 24-Jul-2025 22:36:34 GMT";
    Pragma = "no-cache";
    Server = "daiquiri/5";
    "Strict-Transport-Security" = "max-age=31536000; includeSubDomains";
    "apple-originating-system" = MZContentDeliveryProducer;
    "apple-seq" = "0.0";
    "apple-timing-app" = "257 ms";
    "apple-tk" = false;
    b3 = "13d186535bb2ac9f5c16cad4f15ec7a5-afa78a68d43a6a30";
    "x-apple-application-instance" = 240515;
    "x-apple-application-site" = MR22;
    "x-apple-jingle-correlation-key" = CPIYMU23WKWJ6XAWZLKPCXWHUU;
    "x-apple-request-uuid" = "13d18653-5bb2-ac9f-5c16-cad4f15ec7a5";
    "x-b3-spanid" = afa78a68d43a6a30;
    "x-b3-traceid" = 13d186535bb2ac9f5c16cad4f15ec7a5;
    "x-daiquiri-instance" = "daiquiri:13624001:mr85p00it-hyhk04174601:7987:25RELEASE91:daiquiri-amp-processing-shared-int-001-mr, daiquiri:18493001:mr85p00it-hyhk03154801:7987:25RELEASE91:daiquiri-amp-all-shared-ext-001-mr";
    "x-json-content-length" = 4615;
    "x-request-through-daiquiri-contentdelivery-auth" = "mr-internal";
    "x-responding-instance" = "MZContentDeliveryProducer:240515:::";
    "x-webobjects-loadaverage" = 0;
}
    httpBody: {"id":"20250802032745-020","jsonrpc":"2.0","result":{"ApplicationSettings":{"SkipValidateFirenzeSPIUsage":false,"PerformServerVerification":true,"ITunesSoftwareServiceAllowedPlatforms":[{"restApiPlatformEnum":"IOS","validationMethod":"ios","platformCriteria":{"plistEntries":{"MinimumOSVersion":"","LSRequiresIPhoneOS":""}},"displayName":"iOS App","sdkID":"com.apple.platform.iphoneos","restApiPlatformUTI":"com.apple.ipa","type":1,"xmlID":"ios","serverID":"Purple","platformDirectory":"iPhoneOS.platform"},{"restApiPlatformEnum":"MAC_OS","validationMethod":"osx","platformCriteria":{"plistEntries":{"LSMinimumSystemVersion":""}},"displayName":"macOS App","sdkID":"com.apple.platform.macosx","xmlIDAliases":["macos"],"restApiPlatformUTI":"com.apple.pkg","type":2,"xmlID":"osx","serverID":"Firenze","platformDirectory":"MacOSX.platform"},{"validationMethod":"iap","type":3,"serverID":"InAppPurchase","displayName":"In-App Purchase"},{"restApiPlatformEnum":"TV_OS","validationMethod":"ios","platformCriteria":{"plistEntries":{"UIDeviceFamily":3}},"displayName":"tvOS App","sdkID":"com.apple.platform.appletvos","restApiPlatformUTI":"com.apple.ipa","type":4,"xmlID":"appletvos","serverID":"AppleTVOS","platformDirectory":"AppleTVOS.platform"},{"validationMethod":"ios","type":6,"platformDirectory":"WatchOS.platform","displayName":"watchOS","sdkID":"com.apple.platform.watchos"},{"restApiPlatformEnum":"VISION_OS","validationMethod":"xros","platformCriteria":{"plistEntries":{"UIDeviceFamily":7}},"displayName":"visionOS App","sdkID":"com.apple.platform.xros","xmlIDAliases":["visionos"],"restApiPlatformUTI":"com.apple.ipa","type":7,"xmlID":"xros","serverID":"XROS","platformDirectory":"XROS.platform"}],"IsEnabledForMasteredForItunes":false,"MZWebServiceValidateSoftwareSPIUsageMaximumSize":20971520,"TerritoriesAllowedToAlterPlusQuality":["JP"],"SkipValidatePurpleSPIUsage":false,"Notarization":{"Status":"UNAVAILABLE","Message":"Notarization of MacOS applications using altool has been decommissioned. Please use notarytool. See: https://developer.apple.com/documentation/technotes/tn3147-migrating-to-the-latest-notarization-tool"},"IsDisplayArtistVisible":false,"MZWebServiceTimeOut":900,"ITunesCrashLogServiceAddress":"https://contentdelivery.itunes.apple.com/WebObjects/MZLabelService.woa/json/MZITunesProducerService","SkipValidateProductErrors":true,"IsISBNOptional":true,"softwareUserGuideFilePath":"https://help.apple.com/itc/apploader/","IsPublicationDateOptional":false,"BookSeriesSequenceNumberFormatter":"#.#;0;#0.#","IsVendorIDEditablePreferenceEnabled":true,"AllowsMetadataOnlyInitialImport":false,"CDEnableImportErrorChecking":true,"ShouldPricingTabBeDisplayed":true,"VerifyZipIntegrity":true},"SharedSecret":"07a2d524-12c9-45c2-a081-de1b52739f9b","MultipartUploadsEnabled":true,"AllowedPlatforms":[{"validationMethod":"iap","type":3,"serverID":"InAppPurchase","displayName":"In-App Purchase"},{"restApiPlatformEnum":"MAC_OS","validationMethod":"osx","platformCriteria":{"plistEntries":{"LSMinimumSystemVersion":""}},"displayName":"macOS App","sdkID":"com.apple.platform.macosx","xmlIDAliases":["macos"],"restApiPlatformUTI":"com.apple.pkg","type":2,"xmlID":"osx","serverID":"Firenze","platformDirectory":"MacOSX.platform"},{"restApiPlatformEnum":"IOS","validationMethod":"ios","platformCriteria":{"plistEntries":{"MinimumOSVersion":"","LSRequiresIPhoneOS":""}},"displayName":"iOS App","sdkID":"com.apple.platform.iphoneos","restApiPlatformUTI":"com.apple.ipa","type":1,"xmlID":"ios","serverID":"Purple","platformDirectory":"iPhoneOS.platform"},{"validationMethod":"ios","type":6,"platformDirectory":"WatchOS.platform","displayName":"watchOS","sdkID":"com.apple.platform.watchos"},{"restApiPlatformEnum":"VISION_OS","validationMethod":"xros","platformCriteria":{"plistEntries":{"UIDeviceFamily":7}},"displayName":"visionOS App","sdkID":"com.apple.platform.xros","xmlIDAliases":["visionos"],"restApiPlatformUTI":"com.apple.ipa","type":7,"xmlID":"xros","serverID":"XROS","platformDirectory":"XROS.platform"},{"restApiPlatformEnum":"TV_OS","validationMethod":"ios","platformCriteria":{"plistEntries":{"UIDeviceFamily":3}},"displayName":"tvOS App","sdkID":"com.apple.platform.appletvos","restApiPlatformUTI":"com.apple.ipa","type":4,"xmlID":"appletvos","serverID":"AppleTVOS","platformDirectory":"AppleTVOS.platform"}],"TxHeaders":{"jenga":true},"SessionExpiration":"2025-08-06T03:27:45.698Z","SessionId":"CIyuDBIQsP1bLrCMS4CaghfU9E3JvQ==","ShouldUseRESTAPIs":false,"Success":true,"StreamingSettings":{"LogStreamingEnabled":true,"MultipartUploadsEnabled":true,"AssetDescriptionStreamingEnabled":false}}}
=======================
2025-08-02 03:27:45.845 Web service call (authenticateForSession) result: {
    AllowedPlatforms =     (
                {
            displayName = "In-App Purchase";
            serverID = InAppPurchase;
            type = 3;
            validationMethod = iap;
        },
                {
            displayName = "macOS App";
            platformCriteria =             {
                plistEntries =                 {
                    LSMinimumSystemVersion = "";
                };
            };
            platformDirectory = "MacOSX.platform";
            restApiPlatformEnum = "MAC_OS";
            restApiPlatformUTI = "com.apple.pkg";
            sdkID = "com.apple.platform.macosx";
            serverID = Firenze;
            type = 2;
            validationMethod = osx;
            xmlID = osx;
            xmlIDAliases =             (
                macos
            );
        },
                {
            displayName = "iOS App";
            platformCriteria =             {
                plistEntries =                 {
                    LSRequiresIPhoneOS = "";
                    MinimumOSVersion = "";
                };
            };
            platformDirectory = "iPhoneOS.platform";
            restApiPlatformEnum = IOS;
            restApiPlatformUTI = "com.apple.ipa";
            sdkID = "com.apple.platform.iphoneos";
            serverID = Purple;
            type = 1;
            validationMethod = ios;
            xmlID = ios;
        },
                {
            displayName = watchOS;
            platformDirectory = "WatchOS.platform";
            sdkID = "com.apple.platform.watchos";
            type = 6;
            validationMethod = ios;
        },
                {
            displayName = "visionOS App";
            platformCriteria =             {
                plistEntries =                 {
                    UIDeviceFamily = 7;
                };
            };
            platformDirectory = "XROS.platform";
            restApiPlatformEnum = "VISION_OS";
            restApiPlatformUTI = "com.apple.ipa";
            sdkID = "com.apple.platform.xros";
            serverID = XROS;
            type = 7;
            validationMethod = xros;
            xmlID = xros;
            xmlIDAliases =             (
                visionos
            );
        },
                {
            displayName = "tvOS App";
            platformCriteria =             {
                plistEntries =                 {
                    UIDeviceFamily = 3;
                };
            };
            platformDirectory = "AppleTVOS.platform";
            restApiPlatformEnum = "TV_OS";
            restApiPlatformUTI = "com.apple.ipa";
            sdkID = "com.apple.platform.appletvos";
            serverID = AppleTVOS;
            type = 4;
            validationMethod = ios;
            xmlID = appletvos;
        }
    );
    ApplicationSettings =     {
        AllowsMetadataOnlyInitialImport = 0;
        CDEnableImportErrorChecking = 1;
        ITunesCrashLogServiceAddress = "https://contentdelivery.itunes.apple.com/WebObjects/MZLabelService.woa/json/MZIT\U2026";
        ITunesSoftwareServiceAllowedPlatforms =         (
                        {
                displayName = "iOS App";
                platformCriteria =                 {
                    plistEntries =                     {
                        LSRequiresIPhoneOS = "";
                        MinimumOSVersion = "";
                    };
                };
                platformDirectory = "iPhoneOS.platform";
                restApiPlatformEnum = IOS;
                restApiPlatformUTI = "com.apple.ipa";
                sdkID = "com.apple.platform.iphoneos";
                serverID = Purple;
                type = 1;
                validationMethod = ios;
                xmlID = ios;
            },
                        {
                displayName = "macOS App";
                platformCriteria =                 {
                    plistEntries =                     {
                        LSMinimumSystemVersion = "";
                    };
                };
                platformDirectory = "MacOSX.platform";
                restApiPlatformEnum = "MAC_OS";
                restApiPlatformUTI = "com.apple.pkg";
                sdkID = "com.apple.platform.macosx";
                serverID = Firenze;
                type = 2;
                validationMethod = osx;
                xmlID = osx;
                xmlIDAliases =                 (
                    macos
                );
            },
                        {
                displayName = "In-App Purchase";
                serverID = InAppPurchase;
                type = 3;
                validationMethod = iap;
            },
                        {
                displayName = "tvOS App";
                platformCriteria =                 {
                    plistEntries =                     {
                        UIDeviceFamily = 3;
                    };
                };
                platformDirectory = "AppleTVOS.platform";
                restApiPlatformEnum = "TV_OS";
                restApiPlatformUTI = "com.apple.ipa";
                sdkID = "com.apple.platform.appletvos";
                serverID = AppleTVOS;
                type = 4;
                validationMethod = ios;
                xmlID = appletvos;
            },
                        {
                displayName = watchOS;
                platformDirectory = "WatchOS.platform";
                sdkID = "com.apple.platform.watchos";
                type = 6;
                validationMethod = ios;
            },
                        {
                displayName = "visionOS App";
                platformCriteria =                 {
                    plistEntries =                     {
                        UIDeviceFamily = 7;
                    };
                };
                platformDirectory = "XROS.platform";
                restApiPlatformEnum = "VISION_OS";
                restApiPlatformUTI = "com.apple.ipa";
                sdkID = "com.apple.platform.xros";
                serverID = XROS;
                type = 7;
                validationMethod = xros;
                xmlID = xros;
                xmlIDAliases =                 (
                    visionos
                );
            }
        );
        IsDisplayArtistVisible = 0;
        IsEnabledForMasteredForItunes = 0;
        IsISBNOptional = 1;
        IsPublicationDateOptional = 0;
        IsVendorIDEditablePreferenceEnabled = 1;
        MZWebServiceTimeOut = 900;
        MZWebServiceValidateSoftwareSPIUsageMaximumSize = 20971520;
        Notarization =         {
            Message = "Notarization of MacOS applications using altool has been decommissioned. Please \U2026";
            Status = UNAVAILABLE;
        };
        PerformServerVerification = 1;
        ShouldPricingTabBeDisplayed = 1;
        SkipValidateFirenzeSPIUsage = 0;
        SkipValidateProductErrors = 1;
        SkipValidatePurpleSPIUsage = 0;
        TerritoriesAllowedToAlterPlusQuality =         (
            JP
        );
        VerifyZipIntegrity = 1;
        softwareUserGuideFilePath = "https://help.apple.com/itc/apploader/";
        "\U2026 truncated \U2026" = "\U2026";
    };
    MultipartUploadsEnabled = 1;
    SessionExpiration = "2025-08-06T03:27:45.698Z";
    SessionId = "**hidden value**";
    SharedSecret = "**hidden value**";
    ShouldUseRESTAPIs = 0;
    StreamingSettings =     {
        AssetDescriptionStreamingEnabled = 0;
        LogStreamingEnabled = 1;
        MultipartUploadsEnabled = 1;
    };
    Success = 1;
    TxHeaders =     {
        jenga = 1;
    };
}
2025-08-02 03:27:45.868 *** SESSION AUTH: Jenga directives: {
    jenga = 1;
}
2025-08-02 03:27:45.873 
=======================
WEB SERVICE REQUEST 'allowedPlatformsWithAuthentication'
           URL: https://contentdelivery.itunes.apple.com/WebObjects/MZLabelService.woa/json/MZITunesProducerService
       timeout: 900
        method: POST
   httpHeaders: {
    Authorization = "***";
    "Content-Length" = 457;
    "Content-Type" = "application/json";
    "x-request-id" = "20250802032745-493";
    "x-session-digest" = 801518f3181c1d99478dfcd48d082619;
    "x-session-id" = "CIyuDBIQsP1bLrCMS4CaghfU9E3JvQ==";
    "x-tx-client-name" = altool;
    "x-tx-client-version" = "7.006 (15006)";
    "x-tx-method" = allowedPlatformsWithAuthentication;
}
      httpBody: {
    id = "20250802032745-493";
    jsonrpc = "2.0";
    method = allowedPlatformsWithAuthentication;
    params =     {
        Application = altool;
        ApplicationBundleId = "com.apple.itunes.altool";
        FrameworkVersions =         {
            "com.apple.itunes.connect.ITunesConnectFoundation" = "7.006 (15006)";
            "com.apple.itunes.connect.ITunesConnectFoundationExtras" = "7.006 (15006)";
            "com.apple.itunes.connect.ITunesPackage" = "7.006 (15006)";
        };
        OSIdentifier = "Mac OS X 14.7.6 (arm64)";
        Version = "7.006 (15006)";
    };
}
=======================
2025-08-02 03:27:46.065 
=======================
WEB SERVICE RESPONSE (allowedPlatformsWithAuthentication):
 status code: 200 (no error)
   MIME/TYPE: application/json
 httpHeaders: {
    "Accept-Language" = "en-US,en;q=0.9";
    Authorization = "***";
    "Cache-Control" = "private, no-cache, no-store, must-revalidate, max-age=0";
    "Content-Encoding" = gzip;
    "Content-Length" = 1241;
    "Content-Type" = "application/json";
    Date = "Sat, 02 Aug 2025 03:27:46 GMT";
    Expires = "Thu, 24-Jul-2025 22:41:13 GMT";
    Pragma = "no-cache";
    Server = "daiquiri/5";
    "Strict-Transport-Security" = "max-age=31536000; includeSubDomains";
    "apple-originating-system" = MZContentDeliveryProducer;
    "apple-seq" = "0.0";
    "apple-timing-app" = "31 ms";
    "apple-tk" = false;
    b3 = "76418a6950ec8c319fbe056295db6e64-fb567034579227eb";
    "x-apple-application-instance" = 241313;
    "x-apple-application-site" = MR22;
    "x-apple-jingle-correlation-key" = OZAYU2KQ5SGDDH56AVRJLW3OMQ;
    "x-apple-request-uuid" = "76418a69-50ec-8c31-9fbe-056295db6e64";
    "x-b3-spanid" = fb567034579227eb;
    "x-b3-traceid" = 76418a6950ec8c319fbe056295db6e64;
    "x-daiquiri-instance" = "daiquiri:13624001:mr85p00it-hyhk04174601:7987:25RELEASE91:daiquiri-amp-processing-shared-int-001-mr, daiquiri:18493001:mr85p00it-hyhk03154801:7987:25RELEASE91:daiquiri-amp-all-shared-ext-001-mr";
    "x-json-content-length" = 4456;
    "x-request-through-daiquiri-contentdelivery-auth" = "mr-internal";
    "x-responding-instance" = "MZContentDeliveryProducer:241313:::";
    "x-webobjects-loadaverage" = 0;
}
    httpBody: {"id":"20250802032745-493","jsonrpc":"2.0","result":{"ApplicationSettings":{"SkipValidateFirenzeSPIUsage":false,"PerformServerVerification":true,"ITunesSoftwareServiceAllowedPlatforms":[{"restApiPlatformEnum":"IOS","validationMethod":"ios","platformCriteria":{"plistEntries":{"MinimumOSVersion":"","LSRequiresIPhoneOS":""}},"displayName":"iOS App","sdkID":"com.apple.platform.iphoneos","restApiPlatformUTI":"com.apple.ipa","type":1,"xmlID":"ios","serverID":"Purple","platformDirectory":"iPhoneOS.platform"},{"restApiPlatformEnum":"MAC_OS","validationMethod":"osx","platformCriteria":{"plistEntries":{"LSMinimumSystemVersion":""}},"displayName":"macOS App","sdkID":"com.apple.platform.macosx","xmlIDAliases":["macos"],"restApiPlatformUTI":"com.apple.pkg","type":2,"xmlID":"osx","serverID":"Firenze","platformDirectory":"MacOSX.platform"},{"validationMethod":"iap","type":3,"serverID":"InAppPurchase","displayName":"In-App Purchase"},{"restApiPlatformEnum":"TV_OS","validationMethod":"ios","platformCriteria":{"plistEntries":{"UIDeviceFamily":3}},"displayName":"tvOS App","sdkID":"com.apple.platform.appletvos","restApiPlatformUTI":"com.apple.ipa","type":4,"xmlID":"appletvos","serverID":"AppleTVOS","platformDirectory":"AppleTVOS.platform"},{"validationMethod":"ios","type":6,"platformDirectory":"WatchOS.platform","displayName":"watchOS","sdkID":"com.apple.platform.watchos"},{"restApiPlatformEnum":"VISION_OS","validationMethod":"xros","platformCriteria":{"plistEntries":{"UIDeviceFamily":7}},"displayName":"visionOS App","sdkID":"com.apple.platform.xros","xmlIDAliases":["visionos"],"restApiPlatformUTI":"com.apple.ipa","type":7,"xmlID":"xros","serverID":"XROS","platformDirectory":"XROS.platform"}],"IsEnabledForMasteredForItunes":false,"MZWebServiceValidateSoftwareSPIUsageMaximumSize":20971520,"TerritoriesAllowedToAlterPlusQuality":["JP"],"SkipValidatePurpleSPIUsage":false,"Notarization":{"Status":"UNAVAILABLE","Message":"Notarization of MacOS applications using altool has been decommissioned. Please use notarytool. See: https://developer.apple.com/documentation/technotes/tn3147-migrating-to-the-latest-notarization-tool"},"IsDisplayArtistVisible":false,"MZWebServiceTimeOut":900,"ITunesCrashLogServiceAddress":"https://contentdelivery.itunes.apple.com/WebObjects/MZLabelService.woa/json/MZITunesProducerService","SkipValidateProductErrors":true,"IsISBNOptional":true,"softwareUserGuideFilePath":"https://help.apple.com/itc/apploader/","IsPublicationDateOptional":false,"BookSeriesSequenceNumberFormatter":"#.#;0;#0.#","IsVendorIDEditablePreferenceEnabled":true,"AllowsMetadataOnlyInitialImport":false,"CDEnableImportErrorChecking":true,"ShouldPricingTabBeDisplayed":true,"VerifyZipIntegrity":true},"AllowedPlatforms":[{"validationMethod":"iap","type":3,"serverID":"InAppPurchase","displayName":"In-App Purchase"},{"restApiPlatformEnum":"MAC_OS","validationMethod":"osx","platformCriteria":{"plistEntries":{"LSMinimumSystemVersion":""}},"displayName":"macOS App","sdkID":"com.apple.platform.macosx","xmlIDAliases":["macos"],"restApiPlatformUTI":"com.apple.pkg","type":2,"xmlID":"osx","serverID":"Firenze","platformDirectory":"MacOSX.platform"},{"restApiPlatformEnum":"IOS","validationMethod":"ios","platformCriteria":{"plistEntries":{"MinimumOSVersion":"","LSRequiresIPhoneOS":""}},"displayName":"iOS App","sdkID":"com.apple.platform.iphoneos","restApiPlatformUTI":"com.apple.ipa","type":1,"xmlID":"ios","serverID":"Purple","platformDirectory":"iPhoneOS.platform"},{"validationMethod":"ios","type":6,"platformDirectory":"WatchOS.platform","displayName":"watchOS","sdkID":"com.apple.platform.watchos"},{"restApiPlatformEnum":"VISION_OS","validationMethod":"xros","platformCriteria":{"plistEntries":{"UIDeviceFamily":7}},"displayName":"visionOS App","sdkID":"com.apple.platform.xros","xmlIDAliases":["visionos"],"restApiPlatformUTI":"com.apple.ipa","type":7,"xmlID":"xros","serverID":"XROS","platformDirectory":"XROS.platform"},{"restApiPlatformEnum":"TV_OS","validationMethod":"ios","platformCriteria":{"plistEntries":{"UIDeviceFamily":3}},"displayName":"tvOS App","sdkID":"com.apple.platform.appletvos","restApiPlatformUTI":"com.apple.ipa","type":4,"xmlID":"appletvos","serverID":"AppleTVOS","platformDirectory":"AppleTVOS.platform"}],"ShouldUseRESTAPIs":false,"Success":true,"StreamingSettings":{"LogStreamingEnabled":true,"MultipartUploadsEnabled":true,"AssetDescriptionStreamingEnabled":false},"SessionExpiration":"2025-08-06T03:27:45.698Z"}}
=======================
2025-08-02 03:27:46.071 Web service call (allowedPlatformsWithAuthentication) result: {
    AllowedPlatforms =     (
                {
            displayName = "In-App Purchase";
            serverID = InAppPurchase;
            type = 3;
            validationMethod = iap;
        },
                {
            displayName = "macOS App";
            platformCriteria =             {
                plistEntries =                 {
                    LSMinimumSystemVersion = "";
                };
            };
            platformDirectory = "MacOSX.platform";
            restApiPlatformEnum = "MAC_OS";
            restApiPlatformUTI = "com.apple.pkg";
            sdkID = "com.apple.platform.macosx";
            serverID = Firenze;
            type = 2;
            validationMethod = osx;
            xmlID = osx;
            xmlIDAliases =             (
                macos
            );
        },
                {
            displayName = "iOS App";
            platformCriteria =             {
                plistEntries =                 {
                    LSRequiresIPhoneOS = "";
                    MinimumOSVersion = "";
                };
            };
            platformDirectory = "iPhoneOS.platform";
            restApiPlatformEnum = IOS;
            restApiPlatformUTI = "com.apple.ipa";
            sdkID = "com.apple.platform.iphoneos";
            serverID = Purple;
            type = 1;
            validationMethod = ios;
            xmlID = ios;
        },
                {
            displayName = watchOS;
            platformDirectory = "WatchOS.platform";
            sdkID = "com.apple.platform.watchos";
            type = 6;
            validationMethod = ios;
        },
                {
            displayName = "visionOS App";
            platformCriteria =             {
                plistEntries =                 {
                    UIDeviceFamily = 7;
                };
            };
            platformDirectory = "XROS.platform";
            restApiPlatformEnum = "VISION_OS";
            restApiPlatformUTI = "com.apple.ipa";
            sdkID = "com.apple.platform.xros";
            serverID = XROS;
            type = 7;
            validationMethod = xros;
            xmlID = xros;
            xmlIDAliases =             (
                visionos
            );
        },
                {
            displayName = "tvOS App";
            platformCriteria =             {
                plistEntries =                 {
                    UIDeviceFamily = 3;
                };
            };
            platformDirectory = "AppleTVOS.platform";
            restApiPlatformEnum = "TV_OS";
            restApiPlatformUTI = "com.apple.ipa";
            sdkID = "com.apple.platform.appletvos";
            serverID = AppleTVOS;
            type = 4;
            validationMethod = ios;
            xmlID = appletvos;
        }
    );
    ApplicationSettings =     {
        AllowsMetadataOnlyInitialImport = 0;
        CDEnableImportErrorChecking = 1;
        ITunesCrashLogServiceAddress = "https://contentdelivery.itunes.apple.com/WebObjects/MZLabelService.woa/json/MZIT\U2026";
        ITunesSoftwareServiceAllowedPlatforms =         (
                        {
                displayName = "iOS App";
                platformCriteria =                 {
                    plistEntries =                     {
                        LSRequiresIPhoneOS = "";
                        MinimumOSVersion = "";
                    };
                };
                platformDirectory = "iPhoneOS.platform";
                restApiPlatformEnum = IOS;
                restApiPlatformUTI = "com.apple.ipa";
                sdkID = "com.apple.platform.iphoneos";
                serverID = Purple;
                type = 1;
                validationMethod = ios;
                xmlID = ios;
            },
                        {
                displayName = "macOS App";
                platformCriteria =                 {
                    plistEntries =                     {
                        LSMinimumSystemVersion = "";
                    };
                };
                platformDirectory = "MacOSX.platform";
                restApiPlatformEnum = "MAC_OS";
                restApiPlatformUTI = "com.apple.pkg";
                sdkID = "com.apple.platform.macosx";
                serverID = Firenze;
                type = 2;
                validationMethod = osx;
                xmlID = osx;
                xmlIDAliases =                 (
                    macos
                );
            },
                        {
                displayName = "In-App Purchase";
                serverID = InAppPurchase;
                type = 3;
                validationMethod = iap;
            },
                        {
                displayName = "tvOS App";
                platformCriteria =                 {
                    plistEntries =                     {
                        UIDeviceFamily = 3;
                    };
                };
                platformDirectory = "AppleTVOS.platform";
                restApiPlatformEnum = "TV_OS";
                restApiPlatformUTI = "com.apple.ipa";
                sdkID = "com.apple.platform.appletvos";
                serverID = AppleTVOS;
                type = 4;
                validationMethod = ios;
                xmlID = appletvos;
            },
                        {
                displayName = watchOS;
                platformDirectory = "WatchOS.platform";
                sdkID = "com.apple.platform.watchos";
                type = 6;
                validationMethod = ios;
            },
                        {
                displayName = "visionOS App";
                platformCriteria =                 {
                    plistEntries =                     {
                        UIDeviceFamily = 7;
                    };
                };
                platformDirectory = "XROS.platform";
                restApiPlatformEnum = "VISION_OS";
                restApiPlatformUTI = "com.apple.ipa";
                sdkID = "com.apple.platform.xros";
                serverID = XROS;
                type = 7;
                validationMethod = xros;
                xmlID = xros;
                xmlIDAliases =                 (
                    visionos
                );
            }
        );
        IsDisplayArtistVisible = 0;
        IsEnabledForMasteredForItunes = 0;
        IsISBNOptional = 1;
        IsPublicationDateOptional = 0;
        IsVendorIDEditablePreferenceEnabled = 1;
        MZWebServiceTimeOut = 900;
        MZWebServiceValidateSoftwareSPIUsageMaximumSize = 20971520;
        Notarization =         {
            Message = "Notarization of MacOS applications using altool has been decommissioned. Please \U2026";
            Status = UNAVAILABLE;
        };
        PerformServerVerification = 1;
        ShouldPricingTabBeDisplayed = 1;
        SkipValidateFirenzeSPIUsage = 0;
        SkipValidateProductErrors = 1;
        SkipValidatePurpleSPIUsage = 0;
        TerritoriesAllowedToAlterPlusQuality =         (
            JP
        );
        VerifyZipIntegrity = 1;
        softwareUserGuideFilePath = "https://help.apple.com/itc/apploader/";
        "\U2026 truncated \U2026" = "\U2026";
    };
    SessionExpiration = "2025-08-06T03:27:45.698Z";
    ShouldUseRESTAPIs = 0;
    StreamingSettings =     {
        AssetDescriptionStreamingEnabled = 0;
        LogStreamingEnabled = 1;
        MultipartUploadsEnabled = 1;
    };
    Success = 1;
}
2025-08-02 03:27:46.105 *** Error: Error uploading 'src-tauri/target/x86_64-apple-darwin/release/bundle/macos/Colony.app'.
2025-08-02 03:27:46.105 *** Error: Could not determine the package’s bundle ID. The package is missing an Info.plist or the CFBundlePackageType is not ‘APPL’ or ‘FMWK’. Unable to validate your application. (-21017)
 {
    NSLocalizedDescription = "Could not determine the package\U2019s bundle ID. The package is missing an Info.plist or the CFBundlePackageType is not \U2018APPL\U2019 or \U2018FMWK\U2019.";
    NSLocalizedFailureReason = "Unable to validate your application.";
}
Error: Process completed with exit code 1.
